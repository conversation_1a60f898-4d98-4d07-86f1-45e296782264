import { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="container section">
      <div className="text-center">
        <h1 className="text-accent fade-in">🍽️ Restaurant App</h1>
        <p className="mb-8">Welcome to our delicious restaurant experience!</p>

        {/* Test custom button styles from global.css */}
        <div className="space-y-4">
          <button className="btn btn-primary" onClick={() => setCount(count + 1)}>
            Order Now (Count: {count})
          </button>

          <button className="btn btn-secondary ml-4">
            View Menu
          </button>

          <button className="btn btn-outline ml-4">
            Make Reservation
          </button>
        </div>

        {/* Test card styles from global.css */}
        <div className="card mt-8 slide-up">
          <div className="card-header">
            <h3 className="card-title">Featured Dish</h3>
          </div>
          <div className="card-content">
            <div className="menu-item">
              <div className="menu-item-info">
                <div className="menu-item-name">Grilled Salmon</div>
                <div className="menu-item-description">
                  Fresh Atlantic salmon with herbs and lemon butter sauce
                </div>
              </div>
              <div className="menu-item-price">$24.99</div>
            </div>
          </div>
        </div>

        {/* Test background patterns */}
        <div className="bg-warm p-6 rounded-lg mt-8 bounce-in">
          <h4>Special Offer!</h4>
          <p>Get 20% off your first order when you sign up today!</p>
        </div>
      </div>
    </div>
  )
}

export default App
