/* Tailwind CSS Imports */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Restaurant Global Styles */

/* Custom CSS Variables for Restaurant Theme */
:root {
  /* Restaurant Brand Colors */
  --restaurant-primary: #d97706; /* Warm orange */
  --restaurant-primary-dark: #b45309;
  --restaurant-secondary: #059669; /* Fresh green */
  --restaurant-accent: #dc2626; /* Spicy red */
  --restaurant-warm: #fbbf24; /* Golden yellow */
  --restaurant-neutral: #6b7280; /* Warm gray */

  /* Food Category Colors */
  --appetizer-color: #f59e0b;
  --main-course-color: #dc2626;
  --dessert-color: #8b5cf6;
  --beverage-color: #059669;

  /* Typography */
  --font-heading: 'Playfair Display', serif;
  --font-body: 'Inter', sans-serif;
  --font-accent: 'Dancing Script', cursive;

  /* Spacing */
  --section-padding: 4rem;
  --card-padding: 1.5rem;

  /* Shadows */
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Borders */
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-body);
  line-height: 1.6;
  color: #374151;
  background-color: #fefefe;
  overflow-x: hidden;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: #1f2937;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
}

p {
  margin-bottom: 1rem;
  color: #6b7280;
}

/* Link Styles */
a {
  color: var(--restaurant-primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--restaurant-primary-dark);
  text-decoration: underline;
}

/* Button Base Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 1rem;
  line-height: 1.5;
}

.btn-primary {
  background-color: var(--restaurant-primary);
  color: white;
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
  background-color: var(--restaurant-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  color: white;
  text-decoration: none;
}

.btn-secondary {
  background-color: var(--restaurant-secondary);
  color: white;
  box-shadow: var(--shadow-soft);
}

.btn-secondary:hover {
  background-color: #047857;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  color: white;
  text-decoration: none;
}

.btn-outline {
  background-color: transparent;
  color: var(--restaurant-primary);
  border: 2px solid var(--restaurant-primary);
}

.btn-outline:hover {
  background-color: var(--restaurant-primary);
  color: white;
  text-decoration: none;
}

/* Card Styles */
.card {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-soft);
  padding: var(--card-padding);
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

.card-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.card-content {
  color: #6b7280;
}

/* Menu Item Styles */
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background-color: #fef3e2;
  padding-left: 1rem;
  padding-right: 1rem;
  border-radius: var(--border-radius-md);
  border-bottom: 1px solid transparent;
}

.menu-item-info {
  flex: 1;
}

.menu-item-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.menu-item-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
}

.menu-item-price {
  font-weight: 700;
  color: var(--restaurant-primary);
  font-size: 1.125rem;
  margin-left: 1rem;
}

/* Section Styles */
.section {
  padding: var(--section-padding) 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 3px;
  background: linear-gradient(90deg, var(--restaurant-primary), var(--restaurant-warm));
  border-radius: 2px;
}

/* Container Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 2rem;
  }
}

/* Grid Layouts */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-accent {
  font-family: var(--font-accent);
  color: var(--restaurant-primary);
}

.bg-warm {
  background: linear-gradient(135deg, #fef3e2 0%, #fde68a 100%);
}

.bg-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, #fbbf24 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, #f59e0b 0%, transparent 50%);
  background-size: 100px 100px;
  background-position: 0 0, 50px 50px;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid var(--restaurant-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--restaurant-primary);
  box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --section-padding: 2rem;
    --card-padding: 1rem;
  }

  .menu-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .menu-item-price {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .grid-auto-fit {
    grid-template-columns: 1fr;
  }

  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }
}

/* Print Styles */
@media print {
  .btn,
  .card:hover {
    transform: none;
    box-shadow: none;
  }

  .section {
    padding: 1rem 0;
  }

  .fade-in,
  .slide-up,
  .bounce-in {
    animation: none;
  }
}