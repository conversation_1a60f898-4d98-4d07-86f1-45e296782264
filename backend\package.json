{"name": "backend", "version": "1.0.0", "main": "./src/index.ts", "scripts": {"dev": "nodemon"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "mongodb": "^6.19.0", "mongoose": "^8.18.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.3.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}